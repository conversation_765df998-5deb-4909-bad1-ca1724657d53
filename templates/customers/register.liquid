<!-- /templates/customers/register.liquid -->

<section id="Register" class="center account">
  <div class="wrapper wrapper--narrow">
    <div class="customer-register">
      <h2 class="page__heading">{{ 'customer.register.title' | t }}</h2>

      <p>{{ 'customer.register.sign_in_prompt' | t }} <span class="text-link"><a href="{{ routes.account_login_url }}">{{ 'customer.register.sign_in_cta' | t }}</a></span>.</p>

      {% form 'create_customer', return_to: routes.account_url | append: '/profile' %}
      	<div class="form-field">{{ form.errors | default_errors }}</div>

        <div class="form-field">
          <label for="first_name">{{ 'customer.register.first_name' | t }}</label>
          <input type="text" value="{{ form.first_name }}" name="customer[first_name]" id="first_name" class="large" size="30" placeholder="{{ 'customer.register.first_name' | t }}">
        </div>

        <div class="form-field">
          <label for="last_name">{{ 'customer.register.last_name' | t }}</label>
          <input type="text" value="{{ form.last_name }}" name="customer[last_name]" id="last_name" class="large{% if form.errors contains 'last_name' %} error{% endif %}" placeholder="{{ 'customer.register.last_name' | t }}">
        </div>

        <div class="form-field">
          <label for="email">{{ 'customer.register.email' | t }}</label>
          <input type="email" value="{{ form.email }}" name="customer[email]" id="email" class="large{% if form.errors contains 'email' %} error{% endif %}" placeholder="{{ 'customer.register.email' | t }}">
        </div>

        <div class="form-field">
          <label for="password">{{ 'customer.register.password' | t }}</label>
          <input type="password" value="" name="customer[password]" id="password" class="large password{% if form.errors contains 'password' %} error{% endif %}" placeholder="{{ 'customer.register.password' | t }}">
        </div>

        <div class="form-field action-bottom">
          <button type="submit" class="btn btn--primary btn--solid">
            <span>{{ 'customer.register.submit' | t }}</span>
          </button>
        </div>

        <div class="form__legal">
          {{ 'shopify.online_store.spam_detection.disclaimer_html' | t }}
        </div>
      {% endform %}
    </div>
  </div>
</section>
